2025-05-30 17:10:12,956 - __main__ - INFO - Starting WhatsApp Contact Data Processor
2025-05-30 17:10:12,957 - __main__ - INFO - Processing input JSON files...
2025-05-30 17:10:12,957 - src.data_processor - INFO - Starting phone number extraction
2025-05-30 17:10:12,957 - src.data_processor - INFO - Processing file: data/wa1.json
2025-05-30 17:10:12,967 - src.data_processor - INFO - Extracted 1004 numbers from data/wa1.json
2025-05-30 17:10:12,967 - src.data_processor - INFO - Processing file: data/wa2.json
2025-05-30 17:10:12,979 - src.data_processor - INFO - Extracted 1020 numbers from data/wa2.json
2025-05-30 17:10:12,979 - src.data_processor - INFO - Total unique phone numbers: 1845
2025-05-30 17:10:12,979 - __main__ - INFO - Extracted 1845 unique phone numbers
2025-05-30 17:10:12,983 - __main__ - INFO - Validated 1845 phone numbers
2025-05-30 17:10:12,983 - __main__ - INFO - Starting API data collection...
2025-05-30 17:10:12,983 - src.api_client - INFO - Starting to fetch data for 1845 phone numbers
2025-05-30 17:10:23,107 - src.api_client - WARNING - Timeout for 03038909384 (attempt 1)
2025-05-30 17:10:23,107 - src.api_client - WARNING - Timeout for 03055364703 (attempt 1)
2025-05-30 17:10:35,107 - src.api_client - WARNING - Timeout for 03038909384 (attempt 2)
2025-05-30 17:10:35,108 - src.api_client - WARNING - Timeout for 03055364703 (attempt 2)
2025-05-30 17:10:48,106 - src.api_client - WARNING - Timeout for 03038909384 (attempt 3)
2025-05-30 17:10:48,106 - src.api_client - ERROR - Failed to fetch data for 03038909384 after 3 attempts
2025-05-30 17:10:48,106 - src.api_client - WARNING - Timeout for 03055364703 (attempt 3)
2025-05-30 17:10:48,106 - src.api_client - ERROR - Failed to fetch data for 03055364703 after 3 attempts
2025-05-30 17:10:53,112 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=ClientConnectionError('Connection lost: SSL shutdown timed out')>
TimeoutError: SSL shutdown timed out

The above exception was the direct cause of the following exception:

aiohttp.client_exceptions.ClientConnectionError: Connection lost: SSL shutdown timed out
2025-05-30 17:10:53,112 - asyncio - ERROR - Future exception was never retrieved
future: <Future finished exception=ClientConnectionError('Connection lost: SSL shutdown timed out')>
TimeoutError: SSL shutdown timed out

The above exception was the direct cause of the following exception:

aiohttp.client_exceptions.ClientConnectionError: Connection lost: SSL shutdown timed out
