import asyncio
import logging

from src.config import Config
from src.data_processor import DataProcessor
from src.api_client import APIClient
from src.storage_manager import StorageManager
from src.utils import setup_logging, validate_phone_number


async def main():
    """Main orchestrator function."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("Starting WhatsApp Contact Data Processor")

    try:
        # Initialize components
        config = Config()
        data_processor = DataProcessor(config)
        api_client = APIClient(config)
        storage_manager = StorageManager(config)

        # Process input data
        logger.info("Processing input JSON files...")
        phone_numbers = await data_processor.extract_phone_numbers()
        logger.info(f"Extracted {len(phone_numbers)} unique phone numbers")

        # Validate phone numbers
        valid_numbers = [num for num in phone_numbers if validate_phone_number(num)]
        logger.info(f"Validated {len(valid_numbers)} phone numbers")

        # Process API requests with incremental saving
        logger.info("Starting API data collection...")
        results = await api_client.fetch_all_data(valid_numbers, storage_manager)

        # Final save and summary
        final_output = await storage_manager.finalize_results()
        logger.info(f"Processing complete. Results saved to {final_output}")

        # Print summary
        print("\n=== PROCESSING SUMMARY ===")
        print(f"Total phone numbers processed: {len(valid_numbers)}")
        print(
            f"Successful API calls: {len([r for r in results.values() if not r.get('error')])}"
        )
        print(
            f"Failed API calls: {len([r for r in results.values() if r.get('error')])}"
        )
        print(f"Final results saved to: {final_output}")

    except Exception as e:
        logger.error(f"Fatal error in main process: {e}", exc_info=True)
        raise
    finally:
        await api_client.close()


if __name__ == "__main__":
    asyncio.run(main())
