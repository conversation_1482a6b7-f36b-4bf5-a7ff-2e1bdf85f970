"""
Utility functions for the WhatsApp Contact Data Processor
"""

import re
import logging
import sys
from typing import Optional
from pathlib import Path


def setup_logging(log_file: Optional[Path] = None, level: int = logging.INFO) -> None:
    """Setup logging configuration."""
    if log_file is None:
        log_file = Path("output") / "processing.log"
    
    # Create output directory if it doesn't exist
    log_file.parent.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )


def validate_phone_number(phone_number: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone_number: Phone number string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not phone_number or not isinstance(phone_number, str):
        return False
    
    # Remove spaces and common separators
    cleaned = re.sub(r'[\s\-\(\)]', '', phone_number)
    
    # Check if it's a valid Pakistani number format
    # Should start with 0 (after +92 conversion) and be 11 digits total
    if cleaned.startswith('0') and len(cleaned) == 11:
        # Check if all characters after 0 are digits
        return cleaned[1:].isdigit()
    
    return False


def normalize_phone_number(phone_number: str, prefix: str = "+92") -> str:
    """
    Normalize phone number to standard format.
    
    Args:
        phone_number: Raw phone number string
        prefix: Country prefix to replace
        
    Returns:
        str: Normalized phone number
    """
    if not phone_number:
        return ""
    
    # Convert to string and strip whitespace
    cleaned = str(phone_number).strip()
    
    # Replace country prefix with 0
    if cleaned.startswith(prefix):
        cleaned = cleaned.replace(prefix, '0', 1)
    
    # Remove all spaces
    cleaned = re.sub(r'\s+', '', cleaned)
    
    return cleaned


def sanitize_text(text: str) -> str:
    """
    Sanitize text for safe storage and processing.
    
    Args:
        text: Raw text string
        
    Returns:
        str: Sanitized text
    """
    if not text:
        return ""
    
    # Strip whitespace and normalize spaces
    sanitized = ' '.join(str(text).strip().split())
    
    # Remove or replace problematic characters
    sanitized = sanitized.replace('\n', ' ').replace('\r', ' ')
    
    return sanitized


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        str: Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def create_progress_bar(current: int, total: int, width: int = 50) -> str:
    """
    Create a simple text progress bar.
    
    Args:
        current: Current progress value
        total: Total value
        width: Width of progress bar
        
    Returns:
        str: Progress bar string
    """
    if total == 0:
        return "[" + "=" * width + "] 100%"
    
    progress = current / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = int(progress * 100)
    
    return f"[{bar}] {percentage}% ({current}/{total})"
