import json
import logging
import asyncio
from typing import Dict, Any
from pathlib import Path
from datetime import datetime
import shutil

from src.config import Config
from src.utils import format_file_size


class StorageManager:
    """Manages incremental saving and data integrity."""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.lock = asyncio.Lock()

        # Ensure output directory exists
        self.config.output_dir.mkdir(exist_ok=True)

        # Initialize checkpoint data
        self.checkpoint_data = self._load_existing_checkpoint()

    def _load_existing_checkpoint(self) -> Dict[str, Any]:
        """Load existing checkpoint data if available."""
        if self.config.checkpoint_file.exists():
            try:
                with open(self.config.checkpoint_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                self.logger.info(
                    f"Loaded existing checkpoint with {len(data.get('results', {}))} records"
                )
                return data
            except Exception as e:
                self.logger.warning(f"Could not load checkpoint file: {e}")

        return {
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
                "total_processed": 0,
                "last_updated": None,
            },
            "results": {},
        }

    async def save_checkpoint(self, results: Dict[str, Any]) -> None:
        """
        Save checkpoint data to prevent data loss.

        Args:
            results: Current results to save
        """
        async with self.lock:
            try:
                # Update checkpoint data
                self.checkpoint_data["results"].update(results)
                self.checkpoint_data["metadata"]["total_processed"] = len(
                    self.checkpoint_data["results"]
                )
                self.checkpoint_data["metadata"]["last_updated"] = (
                    datetime.now().isoformat()
                )

                # Create backup of existing checkpoint
                if self.config.checkpoint_file.exists():
                    backup_file = self.config.checkpoint_file.with_suffix(
                        ".backup.json"
                    )
                    shutil.copy2(self.config.checkpoint_file, backup_file)

                # Write new checkpoint
                temp_file = self.config.checkpoint_file.with_suffix(".tmp")
                with open(temp_file, "w", encoding="utf-8") as f:
                    json.dump(self.checkpoint_data, f, indent=2, ensure_ascii=False)

                # Atomic move
                temp_file.replace(self.config.checkpoint_file)

                # Log checkpoint info
                file_size = format_file_size(self.config.checkpoint_file.stat().st_size)
                self.logger.debug(
                    f"Checkpoint saved: {len(self.checkpoint_data['results'])} records, {file_size}"
                )

            except Exception as e:
                self.logger.error(f"Error saving checkpoint: {e}")
                raise

    async def finalize_results(self) -> Path:
        """
        Finalize and save the complete results.

        Returns:
            Path: Path to the final results file
        """
        async with self.lock:
            try:
                # Prepare final data structure
                final_data = {
                    "metadata": {
                        "processing_completed_at": datetime.now().isoformat(),
                        "total_phone_numbers": len(self.checkpoint_data["results"]),
                        "successful_requests": len(
                            [
                                r
                                for r in self.checkpoint_data["results"].values()
                                if r.get("success", False)
                            ]
                        ),
                        "failed_requests": len(
                            [
                                r
                                for r in self.checkpoint_data["results"].values()
                                if not r.get("success", False)
                            ]
                        ),
                        "version": "1.0",
                    },
                    "results": self.checkpoint_data["results"],
                }

                # Add processing statistics
                final_data["statistics"] = self._generate_statistics(
                    final_data["results"]
                )

                # Save final results
                with open(self.config.final_output_file, "w", encoding="utf-8") as f:
                    json.dump(final_data, f, indent=2, ensure_ascii=False)

                # Create a compressed version for large datasets
                if (
                    self.config.final_output_file.stat().st_size > 10 * 1024 * 1024
                ):  # 10MB
                    await self._create_compressed_output(final_data)

                file_size = format_file_size(
                    self.config.final_output_file.stat().st_size
                )
                self.logger.info(f"Final results saved: {file_size}")

                return self.config.final_output_file

            except Exception as e:
                self.logger.error(f"Error finalizing results: {e}")
                raise

    def _generate_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate processing statistics."""
        stats = {
            "total_numbers": len(results),
            "successful_requests": 0,
            "failed_requests": 0,
            "total_records_found": 0,
            "numbers_with_data": 0,
            "numbers_without_data": 0,
            "average_records_per_number": 0,
            "processing_errors": [],
        }

        total_records = 0

        for phone_number, result in results.items():
            if result.get("success", False):
                stats["successful_requests"] += 1
                data = result.get("data", [])
                if data:
                    stats["numbers_with_data"] += 1
                    total_records += len(data)
                else:
                    stats["numbers_without_data"] += 1
            else:
                stats["failed_requests"] += 1
                error = result.get("error", "Unknown error")
                stats["processing_errors"].append(f"{phone_number}: {error}")

        stats["total_records_found"] = total_records
        if stats["numbers_with_data"] > 0:
            stats["average_records_per_number"] = (
                total_records / stats["numbers_with_data"]
            )

        return stats

    async def _create_compressed_output(self, data: Dict[str, Any]) -> None:
        """Create a compressed version of the output for large datasets."""
        try:
            import gzip

            compressed_file = self.config.final_output_file.with_suffix(".json.gz")

            with gzip.open(compressed_file, "wt", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            original_size = format_file_size(
                self.config.final_output_file.stat().st_size
            )
            compressed_size = format_file_size(compressed_file.stat().st_size)

            self.logger.info(
                f"Compressed output created: {original_size} -> {compressed_size}"
            )

        except ImportError:
            self.logger.warning("gzip not available, skipping compression")
        except Exception as e:
            self.logger.warning(f"Could not create compressed output: {e}")

    def get_progress_info(self) -> Dict[str, Any]:
        """Get current progress information."""
        return {
            "total_processed": len(self.checkpoint_data["results"]),
            "successful": len(
                [
                    r
                    for r in self.checkpoint_data["results"].values()
                    if r.get("success", False)
                ]
            ),
            "failed": len(
                [
                    r
                    for r in self.checkpoint_data["results"].values()
                    if not r.get("success", False)
                ]
            ),
            "last_updated": self.checkpoint_data["metadata"].get("last_updated"),
            "checkpoint_file_size": format_file_size(
                self.config.checkpoint_file.stat().st_size
            )
            if self.config.checkpoint_file.exists()
            else "0 B",
        }

    async def cleanup_old_files(self, keep_backups: int = 5) -> None:
        """Clean up old backup files."""
        try:
            backup_pattern = f"{self.config.checkpoint_file.stem}.backup*.json"
            backup_files = sorted(
                self.config.output_dir.glob(backup_pattern),
                key=lambda x: x.stat().st_mtime,
                reverse=True,
            )

            # Keep only the most recent backups
            for backup_file in backup_files[keep_backups:]:
                backup_file.unlink()
                self.logger.debug(f"Removed old backup: {backup_file}")

        except Exception as e:
            self.logger.warning(f"Error cleaning up old files: {e}")
