import json
import logging
import pandas as pd
from typing import List, Set, Dict, Any
from pathlib import Path

from src.config import Config
from src.utils import normalize_phone_number, validate_phone_number


class DataProcessor:
    """Handles processing of WhatsApp contact data from JSON files."""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

    async def extract_phone_numbers(self) -> List[str]:
        """
        Extract and process phone numbers from input JSON files.

        Returns:
            List[str]: List of unique, normalized phone numbers
        """
        self.logger.info("Starting phone number extraction")

        all_numbers: Set[str] = set()

        for file_path in self.config.input_files:
            self.logger.info(f"Processing file: {file_path}")
            numbers = await self._process_single_file(file_path)
            all_numbers.update(numbers)
            self.logger.info(f"Extracted {len(numbers)} numbers from {file_path}")

        # Convert to sorted list for consistent processing order
        unique_numbers = sorted(list(all_numbers))
        self.logger.info(f"Total unique phone numbers: {len(unique_numbers)}")

        return unique_numbers

    async def _process_single_file(self, file_path: Path) -> Set[str]:
        """
        Process a single JSON file and extract phone numbers.

        Args:
            file_path: Path to the JSON file

        Returns:
            Set[str]: Set of normalized phone numbers from the file
        """
        try:
            # Load JSON data
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Convert to DataFrame for easier processing
            df = pd.DataFrame(data)

            # Extract phone numbers from specified columns
            phone_numbers = set()

            for column in self.config.columns_to_extract:
                if column in df.columns:
                    # Extract non-null values that start with the phone prefix
                    column_numbers = df[column].dropna()

                    # Filter for phone numbers starting with the prefix
                    phone_mask = column_numbers.astype(str).str.startswith(
                        self.config.phone_prefix, na=False
                    )
                    filtered_numbers = column_numbers[phone_mask]

                    # Normalize phone numbers
                    for number in filtered_numbers:
                        normalized = normalize_phone_number(
                            str(number), self.config.phone_prefix
                        )
                        if normalized and validate_phone_number(normalized):
                            phone_numbers.add(normalized)

            return phone_numbers

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
            raise

    def get_data_statistics(self, file_path: Path) -> Dict[str, Any]:
        """
        Get statistics about a data file.

        Args:
            file_path: Path to the JSON file

        Returns:
            Dict[str, Any]: Statistics about the file
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            df = pd.DataFrame(data)

            stats = {
                "file_name": file_path.name,
                "total_records": len(df),
                "columns": list(df.columns),
                "memory_usage": df.memory_usage(deep=True).sum(),
            }

            # Count phone numbers in each relevant column
            for column in self.config.columns_to_extract:
                if column in df.columns:
                    phone_count = (
                        df[column]
                        .astype(str)
                        .str.startswith(self.config.phone_prefix, na=False)
                        .sum()
                    )
                    stats[f"{column}_phone_count"] = phone_count

            return stats

        except Exception as e:
            self.logger.error(f"Error getting statistics for {file_path}: {e}")
            return {"error": str(e)}

    async def validate_extracted_data(self, phone_numbers: List[str]) -> Dict[str, Any]:
        """
        Validate the extracted phone numbers.

        Args:
            phone_numbers: List of phone numbers to validate

        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {
            "total_numbers": len(phone_numbers),
            "valid_numbers": 0,
            "invalid_numbers": 0,
            "duplicate_numbers": 0,
            "validation_errors": [],
        }

        seen_numbers = set()

        for number in phone_numbers:
            if number in seen_numbers:
                validation_results["duplicate_numbers"] += 1
                continue

            seen_numbers.add(number)

            if validate_phone_number(number):
                validation_results["valid_numbers"] += 1
            else:
                validation_results["invalid_numbers"] += 1
                validation_results["validation_errors"].append(
                    f"Invalid number: {number}"
                )

        self.logger.info(
            f"Validation complete: {validation_results['valid_numbers']} valid, "
            f"{validation_results['invalid_numbers']} invalid numbers"
        )

        return validation_results
