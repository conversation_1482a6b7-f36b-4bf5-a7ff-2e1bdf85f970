from pathlib import Path
from typing import Dict, Any


class Config:
    """Configuration class for the application."""

    def __init__(self):
        # File paths
        self.input_files = [Path("data/wa1.json"), Path("data/wa2.json")]

        # Output configuration
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)

        self.checkpoint_file = self.output_dir / "checkpoint.json"
        self.final_output_file = self.output_dir / "final_results.json"
        self.log_file = self.output_dir / "processing.log"

        # API configuration
        self.api_base_url = "https://simownerdetails.net.pk/wp-admin/admin-ajax.php"
        self.api_timeout = 10
        self.max_retries = 3
        self.retry_delay = 1  # seconds

        # Rate limiting
        self.max_concurrent_requests = 5
        self.requests_per_second = 2

        # Data processing configuration
        self.phone_prefix = "+92"
        self.columns_to_extract = ["_ao3e", "x1iyjqo2"]

        # Checkpoint configuration
        self.checkpoint_interval = 10  # Save every N successful requests

        # Validation settings
        self.min_phone_length = 10
        self.max_phone_length = 15

    def get_api_url(self, phone_number: str) -> str:
        """Generate API URL for a phone number."""
        return f"{self.api_base_url}?action=get_number_data&get_number_data=searchdata={phone_number}"

    def validate_config(self) -> bool:
        """Validate configuration settings."""
        # Check if input files exist
        for file_path in self.input_files:
            if not file_path.exists():
                raise FileNotFoundError(f"Input file not found: {file_path}")

        # Validate rate limiting settings
        if self.max_concurrent_requests <= 0:
            raise ValueError("max_concurrent_requests must be positive")

        if self.requests_per_second <= 0:
            raise ValueError("requests_per_second must be positive")

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary for logging/debugging."""
        return {
            "input_files": [str(f) for f in self.input_files],
            "output_dir": str(self.output_dir),
            "api_base_url": self.api_base_url,
            "max_concurrent_requests": self.max_concurrent_requests,
            "requests_per_second": self.requests_per_second,
            "checkpoint_interval": self.checkpoint_interval,
        }
