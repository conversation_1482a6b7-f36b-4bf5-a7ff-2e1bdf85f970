import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from datetime import datetime

from src.config import Config
from src.utils import sanitize_text, create_progress_bar


class APIClient:
    """Async API client for fetching phone number data."""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)
        self.rate_limiter = asyncio.Semaphore(config.requests_per_second)

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.config.api_timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()

    async def fetch_single_number(self, phone_number: str) -> Dict[str, Any]:
        """
        Fetch data for a single phone number with retry logic.

        Args:
            phone_number: Phone number to fetch data for

        Returns:
            Dict[str, Any]: API response data or error information
        """
        async with self.semaphore:  # Limit concurrent requests
            async with self.rate_limiter:  # Rate limiting
                for attempt in range(self.config.max_retries):
                    try:
                        session = await self._get_session()
                        url = self.config.get_api_url(phone_number)

                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                parsed_data = self._parse_api_response(
                                    phone_number, data
                                )

                                self.logger.debug(
                                    f"Successfully fetched data for {phone_number}"
                                )
                                return {
                                    "phone_number": phone_number,
                                    "success": True,
                                    "data": parsed_data,
                                    "timestamp": datetime.now().isoformat(),
                                    "attempts": attempt + 1,
                                }
                            else:
                                self.logger.warning(
                                    f"HTTP {response.status} for {phone_number}"
                                )

                    except asyncio.TimeoutError:
                        self.logger.warning(
                            f"Timeout for {phone_number} (attempt {attempt + 1})"
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"Error for {phone_number} (attempt {attempt + 1}): {e}"
                        )

                    if attempt < self.config.max_retries - 1:
                        await asyncio.sleep(self.config.retry_delay * (attempt + 1))

                # All retries failed
                self.logger.error(
                    f"Failed to fetch data for {phone_number} after {self.config.max_retries} attempts"
                )
                return {
                    "phone_number": phone_number,
                    "success": False,
                    "error": "Max retries exceeded",
                    "timestamp": datetime.now().isoformat(),
                    "attempts": self.config.max_retries,
                }

    def _parse_api_response(
        self, phone_number: str, response_data: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """
        Parse API response and extract structured data.

        Args:
            phone_number: Phone number being processed
            response_data: Raw API response

        Returns:
            List[Dict[str, str]]: Parsed records
        """
        try:
            if not response_data.get("success"):
                return []

            html_content = response_data.get("data", "")
            if not html_content:
                return []

            soup = BeautifulSoup(html_content, "html.parser")
            records = []

            for card in soup.find_all("div", class_="result-card"):
                record = self._parse_single_result_card(card)
                if record:
                    record["source_phone"] = phone_number
                    records.append(record)

            return records

        except Exception as e:
            self.logger.error(f"Error parsing response for {phone_number}: {e}")
            return []

    def _parse_single_result_card(
        self, card: BeautifulSoup
    ) -> Optional[Dict[str, str]]:
        """
        Parse a single result card from HTML.

        Args:
            card: BeautifulSoup element representing a result card

        Returns:
            Optional[Dict[str, str]]: Parsed record or None
        """
        record = {}

        try:
            for field in card.find_all("div", class_="field"):
                label = field.find("label", class_="info")
                value_div = field.find("div")

                if not label or not value_div:
                    continue

                # Clean and normalize label
                label_text = label.get_text(strip=True)
                label_text = label_text.replace("#", "").replace(" ", "_").lower()

                # Clean and normalize value
                value_text = value_div.get_text(strip=True)
                value_text = sanitize_text(value_text)

                # Skip empty or meaningless values
                if value_text.lower() not in ("", "unknown", "record not found", "n/a"):
                    record[label_text] = value_text

            return record if record else None

        except Exception as e:
            self.logger.error(f"Error parsing result card: {e}")
            return None

    async def fetch_all_data(
        self, phone_numbers: List[str], storage_manager
    ) -> Dict[str, Any]:
        """
        Fetch data for all phone numbers with progress tracking and incremental saving.

        Args:
            phone_numbers: List of phone numbers to process
            storage_manager: Storage manager for incremental saving

        Returns:
            Dict[str, Any]: All results
        """
        self.logger.info(
            f"Starting to fetch data for {len(phone_numbers)} phone numbers"
        )

        results = {}
        completed = 0

        # Create tasks for all phone numbers
        tasks = [self.fetch_single_number(number) for number in phone_numbers]

        # Process tasks with progress tracking
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                phone_number = result["phone_number"]
                results[phone_number] = result
                completed += 1

                # Save checkpoint periodically
                if completed % self.config.checkpoint_interval == 0:
                    await storage_manager.save_checkpoint(results)
                    self.logger.info(
                        f"Checkpoint saved at {completed} completed requests"
                    )

                # Show progress
                progress = create_progress_bar(completed, len(phone_numbers))
                print(f"\rProgress: {progress}", end="", flush=True)

                # Rate limiting between requests
                await asyncio.sleep(1.0 / self.config.requests_per_second)

            except Exception as e:
                self.logger.error(f"Error processing task: {e}")
                completed += 1

        print()  # New line after progress bar

        # Final checkpoint save
        await storage_manager.save_checkpoint(results)
        self.logger.info(
            f"Completed fetching data for {len(phone_numbers)} phone numbers"
        )

        return results
